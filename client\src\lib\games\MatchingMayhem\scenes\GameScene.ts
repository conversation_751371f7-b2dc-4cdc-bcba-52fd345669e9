import * as Phaser from 'phaser';
import MatchingCard from '../objects/MatchingCard';


import LivesManager from '../../managers/LivesManager';

import RadialT<PERSON>r<PERSON> from '../ui/RadialTimerUI';
import { GAME_CONFIG, CATEGORY_TINTS } from '../config/GameConfig';


import { gameActions, gameState } from '$lib/stores';
import { get } from 'svelte/store';
import type { SocketClient } from '$lib/socket';
import type { MatchingMayhemConfig } from '../index';
import type { ClientRoundData, ClientCardData } from '../types/types';

export default class GameScene extends Phaser.Scene {
  // 2D array to store animal images by [animal][color]
  private animalImages: string[][] = [];

  // Game elements
  private mainImage!: Phaser.GameObjects.Image;
  private optionCards: MatchingCard[] = [];


  // UI elements
  // private scoreText!: any; // Custom object with setText method

  private radialTimerUI!: RadialTimerUI;
  private bonusScoreText!: Phaser.GameObjects.Text;
  
  private UIContainer!: Phaser.GameObjects.Container;



  private livesManager!: LivesManager;


  // UI panels

  private gamePanel!: Phaser.GameObjects.Container;

  // Game state
  // private score: number = 0;
  private isLocked: boolean = false;
  private isGameOver: boolean = false;
  private currentRoundTime: number = 0;




  private socketClient: SocketClient | null = null;

  // Game session data
  private roomId: string = 'room-' + Date.now().toString(36);
  private gameId: string = 'matching-mayhem';

  // Server-side game state
  private currentRoundData: any = null;
  private isWaitingForServer: boolean = false;

  constructor() {
    super('GameScene');

    console.log('Matching Mayhem Game initialized');
  }

  init(){
    // Reset game state
    this.isGameOver = false;
    this.isWaitingForServer = false;

    // Get socket client from game registry
    const gameConfig = this.registry.get('gameConfig') as MatchingMayhemConfig;
    this.socketClient = gameConfig?.socketClient || null;
    this.roomId = gameConfig?.roomId || 'room-' + Date.now().toString(36);
    this.gameId = gameConfig?.gameId || 'matching-mayhem';

    // Setup socket event listeners
    this.setupSocketEventListeners();

    this.livesManager = new LivesManager(this);
  }

  /**
   * Setup socket event listeners for server communication
   */
  private setupSocketEventListeners(): void {
    if (!this.socketClient) return;

    // Add custom event listeners for game-specific handling
    this.socketClient.addCustomEventListener('started', (data: any) => {
      console.log('Matching Mayhem game started by server:', data);

      // Sync game state from server
      if (data.firstRound) {
        console.log('First round data received from server:', data.firstRound);

        this.currentRoundData = data.firstRound;
        this.setupRoundFromServer(data.firstRound);
      }

      // Start the local game UI with server-provided data
      this.startGame();
    });

    this.socketClient.addCustomEventListener('action_result', (data: any) => {
      console.log('Action result from server:', data);
      if (data.actionType === 'card_select') {
        this.handleCardSelectResult(data.data);
      }
    });

    this.socketClient.addCustomEventListener('ended', (data: any) => {
      console.log('Game ended by server:', data);
      this.endGame();
    });

    this.socketClient.addCustomEventListener('game_error', (data: any) => {
      console.error('Game error from server:', data);
    });

    this.socketClient.addCustomEventListener('round_timer_tick', (data: any) => {
      console.log('Round timer tick from server:', data);
      this.handleRoundTimerTick(data);
    });

    console.log('Socket event listeners setup for Matching Mayhem GameScene');
  }

  create(): void {
    // Get the animal images from the registry
    this.animalImages = this.game.registry.get('animalImages') || [];

    // Set a solid background color that matches the game_bg.png
    this.cameras.main.setBackgroundColor(GAME_CONFIG.BACKGROUND_COLOR);

    // Create a separate background container with gradient that matches the game background
    const backgroundContainer = this.add.container(0, 0);
    backgroundContainer.setDepth(-10); // Extremely low depth to ensure it's behind everything

    // Create a gradient background that matches the game_bg.png
    // First, create a canvas texture for the gradient
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;
    const gradientTexture = this.textures.createCanvas('gradientBg', width, height);

    if (gradientTexture) {
      const context = gradientTexture.getContext();

      // Create a radial gradient from center to edges (dark blue to darker blue)
      const gradient = context.createRadialGradient(
        width/2, height/2, 0,           // inner circle center and radius
        width/2, height/2, height * 0.8  // outer circle center and radius
      );

      // Add color stops that match the game_bg.png
      gradient.addColorStop(0, '#151B30');  // Dark blue at center
      gradient.addColorStop(1, '#0E0F1E');  // Darker blue at edges

      // Fill the canvas with the gradient
      context.fillStyle = gradient;
      context.fillRect(0, 0, width, height);

      // Add some subtle noise/texture
      for (let i = 0; i < 5000; i++) {
        const x = Math.random() * width;
        const y = Math.random() * height;
        const size = Math.random() * 2;
        const alpha = Math.random() * 0.05; // Very subtle

        context.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        context.fillRect(x, y, size, size);
      }

      // Refresh the texture
      gradientTexture.refresh();

      // Create an image using this texture
      const gradientBg = this.add.image(0, 0, 'gradientBg').setOrigin(0, 0);
      backgroundContainer.add(gradientBg);

      console.log('Created gradient background as fallback');
    } else {
      // If canvas creation fails, fall back to a simple rectangle
      const baseRect = this.add.rectangle(
        0, 0,
        this.cameras.main.width,
        this.cameras.main.height,
        0x0E0F1E // Dark blue color
      ).setOrigin(0, 0);
      backgroundContainer.add(baseRect);
    }

    // Create the background image with explicit dimensions
    const bgImage = this.add.image(0, 0, 'game_background').setOrigin(0, 0);

    // Scale the image to fit the screen
    bgImage.displayWidth = this.cameras.main.width;
    bgImage.displayHeight = this.cameras.main.height;
    backgroundContainer.add(bgImage);

    // Create the main game panel
    this.gamePanel = this.add.container(0, 0);
    this.gamePanel.setVisible(false);
    this.gamePanel.setDepth(1);

    // Create UI elements in the game panel
    this.createUI();

    // Start the countdown
    this.startCountdown();
  }

  shutdown() {
    // Clean up managers



    // Clean up radial timer UI
    if (this.radialTimerUI) {
      this.radialTimerUI.destroy();
    }

    if (this.livesManager) {
      this.livesManager.destroy();
    }
  }

  private async startCountdown(): Promise<void> {
    for (let i = 0; i < 4; i++) {
      // Play sound
      try {
        this.sound.play(i === 3 ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      await new Promise<void>((resolve) => {
        this.time.delayedCall(1300, () => resolve());
      });
    }

    // Send game start event to server using proper format
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.startGame(this.gameId, this.roomId);
    }
  }

  private createUI(): void {
    this.UIContainer = this.add.container(0, 0);
    this.gamePanel.add(this.UIContainer);

    this.createCenter();

    this.createRoundTimerUI();
  }

  private createCenter(){
    // Option cards - in a plus-shaped pattern with arrows pointing to the center
    const cardOffset = 110; // Reduced distance from center
    const centerX = this.cameras.main.width / 2;
    const centerY = this.cameras.main.height * 0.55;

    // Card positions in plus-shaped pattern with arrows pointing to center
    const cardPositions = [
      { x: centerX - cardOffset, y: centerY - cardOffset }, // Top left
      { x: centerX + cardOffset, y: centerY - cardOffset }, // Top right
      { x: centerX, y: centerY }, // Center
      { x: centerX - cardOffset, y: centerY + cardOffset }, // Bottom left
      { x: centerX + cardOffset, y: centerY + cardOffset }  // Bottom right
    ];

    // Create all cards first
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      // Create the card
      const card = new MatchingCard(
        this,
        cardPositions[i].x,
        cardPositions[i].y,
        GAME_CONFIG.CARD_SIZE
      );

      // Store card in array but don't add to gamePanel yet
      this.optionCards.push(card);

      // Only add click handler to non-center cards
      if (i !== 2) {
        card.on('pointerdown', () => {
          this.checkAnswer(card.getCardId());
        });
      }
    }

    // Now add cards to the gamePanel in the correct order (bottom to top)
    // First add all corner cards
    for (let i = 0; i < GAME_CONFIG.OPTION_CARDS_COUNT; i++) {
      if (i !== 2) { // Skip center card for now
        this.gamePanel.add(this.optionCards[i]);
        this.optionCards[i].setDepth(1); // Set lower depth for outer cards
      }
    }

    // Now add center card with special styling
    const centerCard = this.optionCards[2];

    // Add a solid backdrop behind the center card to block visibility of cards underneath
    // Use Graphics for rounded rectangle since Rectangle doesn't support rounded corners
    const solidBackdrop = this.add.graphics();
    solidBackdrop.fillStyle(0x181818, 1); // Dark color matching the background

    // Make backdrop slightly smaller to ensure proper coverage
    const backdropSize = GAME_CONFIG.CARD_SIZE * 0.9;
    solidBackdrop.fillRoundedRect(
      cardPositions[2].x - backdropSize/2,
      cardPositions[2].y - backdropSize/2,
      backdropSize,
      backdropSize,
      16 // Corner radius
    );
    this.gamePanel.add(solidBackdrop);
    solidBackdrop.setDepth(8); // Behind the glow but above other cards

    // Add glowing border effect to the center card
    const borderGlow = this.add.image(cardPositions[2].x, cardPositions[2].y, 'card_bg')
      .setOrigin(0.5)
      .setDisplaySize(GAME_CONFIG.CARD_SIZE + 5, GAME_CONFIG.CARD_SIZE + 5) // Reduced size difference (was +10)
      .setTint(0x33DDFF) // Cyan color matching the timer
      .setAlpha(0.4); // Reduced from 0.8 to 0.4 (50% decrease)

    // Add border glow first, then center card
    this.gamePanel.add(borderGlow);
    borderGlow.setDepth(9); // Just below the card but above other cards

    // Finally add center card on top of everything
    this.gamePanel.add(centerCard);
    centerCard.setDepth(10); // Higher depth value so it renders on top

    // Center card fully opaque
    centerCard.getCardBackground().setAlpha(1);

    // Disable interactivity on center card since it's the reference
    centerCard.disableInteractive();

    // Debug logging for center card setup
    console.log('Center card added to game panel');
    console.log('Center card position:', centerCard.x, centerCard.y);
    console.log('Center card visible:', centerCard.visible);
    console.log('Center card alpha:', centerCard.alpha);
    console.log('Game panel children count:', this.gamePanel.list.length);
  }

  private createRoundTimerUI(): void {
    const { width, height } = this.cameras.main;

    // Create the radial timer UI
    this.radialTimerUI = new RadialTimerUI(this, {
      x: width / 2,
      y: height * 0.55,
      size: GAME_CONFIG.CARD_SIZE * 3,
      cornerRadius: 16,
      borderWidth: 8,
    });

    // Create and add to game panel
    this.radialTimerUI.create();
    this.radialTimerUI.setDepth(7); // Behind the center card backdrop but above other cards

    // Add to game panel
    const container = this.radialTimerUI.getContainer();
    if (container) {
      this.gamePanel.add(container);
    }

    // Initialize with full progress
    this.radialTimerUI.updateProgress(1.0);
  }

  private updateRadialTimer(progress: number): void {
    if (!this.radialTimerUI) return;

    // Update the radial timer UI with the current progress
    this.radialTimerUI.updateProgress(progress);
  }

  /**
   * Start the game with server-provided data
   */
  private startGame(): void {
    console.log('Starting game with server data');
    gameActions.startGame();

    // Make the game panel visible now that the game has started
    this.gamePanel.setVisible(true);
    console.log('Game panel made visible');

    // The round setup will be handled by setupRoundFromServer when server data arrives
  }

  /**
   * Setup a round using data from the server
   */
  private setupRoundFromServer(roundData: ClientRoundData): void {
    console.log('Setting up round from server:', roundData);

    // Reset lock state
    this.isLocked = false;
    this.isWaitingForServer = false;

    // Initialize round timer based on server data
    this.currentRoundTime = roundData.timeLimit / 1000; // Convert from ms to seconds

    // Initialize radial timer with full progress
    this.updateRadialTimer(1.0);

    // Set up cards based on server data
    this.setupCardsFromServerData(roundData);
  }

  /**
   * Setup cards using server-provided round data
   */
  private setupCardsFromServerData(roundData: ClientRoundData): void {
    console.log('Setting up cards from server data:', roundData);

    // Set up the main image (hidden reference card)
    const mainImageKey = roundData.mainCard.imageKey;
    this.mainImage?.setTexture(mainImageKey);

    // Set up the 5 option cards using server data
    for (let i = 0; i < this.optionCards.length; i++) {
      // Reset card selection state
      this.optionCards[i].resetSelection();

      const cardData = roundData.cards[i];
      if (cardData) {
        // Set card ID and image properties from server
        this.optionCards[i].setCardId(cardData.id);
        this.optionCards[i].setCardImage(cardData.imageKey);

        // Set the tint based on color category
        this.optionCards[i].setTint(CATEGORY_TINTS[cardData.colorIndex]);

        console.log(`Card ${i}: ${cardData.imageKey}, ID: ${cardData.id}, position: ${cardData.position}`);

      } else {
        console.error(`No card data provided for card ${i}`);
      }

      // Animate card image appearing with delay
      this.optionCards[i].animateCardImage(200 + i * 50);
    }
  }

  /**
   * Handle round timer tick updates from server
   */
  private handleRoundTimerTick(data: any): void {
    if (this.isLocked || this.isGameOver) return;

    const { remainingTime, progress } = data;

    // Update current round time from server
    this.currentRoundTime = remainingTime / 1000; // Convert from ms to seconds

    // Update the radial timer with server-provided progress
    this.updateRadialTimer(progress);

    console.log(`Round timer update: ${this.currentRoundTime.toFixed(2)}s remaining (${(progress * 100).toFixed(1)}%)`);
  }

  private checkAnswer(cardId: string): void {
    // Ignore if game is locked, over, or waiting for server
    if (this.isLocked || this.isGameOver || this.isWaitingForServer) return;

    // Lock the game temporarily and mark as waiting for server
    this.isLocked = true;
    this.isWaitingForServer = true;

    // Calculate reaction time
    const reactionTime = this.currentRoundData ?
      Date.now() - this.currentRoundData.startTime : 0;

    console.log('Card selected:', cardId, 'Reaction time:', reactionTime);

    // Send card selection to server - socket connection is required
    if (this.socketClient && this.socketClient.isConnected()) {
      this.socketClient.sendCardSelect(this.gameId, this.roomId, cardId, reactionTime);
    } else {
      console.error('Socket client not connected - game requires server connection');
      // Reset lock state since we can't process the action
      this.isLocked = false;
      this.isWaitingForServer = false;
    }
  }

  /**
   * Find a card by its ID
   */
  private findCardById(cardId: string): MatchingCard | null {
    return this.optionCards.find(card => card.getCardId() === cardId) || null;
  }

  /**
   * Handle card selection result from server
   */
  private handleCardSelectResult(data: any): void {
    console.log('Handling card select result:', data);

    const { cardId, isCorrect, points, newScore, newLives, gameEnded, nextRound, correctCardId } = data;

    // Find the selected card by ID
    const selectedCard = this.findCardById(cardId);
    const correctCard = this.findCardById(correctCardId);

    // Show the correct card briefly if it's different from the selected card
    if (correctCard && correctCardId !== cardId) {
      correctCard.markSelected(true);
    }

    if (isCorrect) {
      // Correct answer
      this.sound.play('correct');
      if (selectedCard) {
        selectedCard.markSelected(true);
      }

      // Update score
      gameActions.updateScore(newScore);

      // Show bonus animation
      this.showBonusText(`Good Choice +${points}`, true);
    } else {
      // Wrong answer
      this.sound.play('wrong');
      if (selectedCard) {
        selectedCard.markSelected(false);
      }

      // Update score and lives
      gameActions.updateScore(newScore);
      gameActions.updateLives(newLives);

      this.livesManager.deductHeart(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2,
        this.gamePanel
      );

      // Show penalty animation
      this.showBonusText(`Bad Choice -${points}`, false);
    }

    // Handle game end or next round
    if (gameEnded) {
      this.time.delayedCall(1000, () => {
        this.endGame();
      });
    } else if (nextRound) {
      // Set up next round after delay
      this.time.delayedCall(780, () => {
        this.setupRoundFromServer(nextRound);
      });
    } else {
      // Re-enable interaction if no next round yet
      this.time.delayedCall(500, () => {
        this.isLocked = false;
        this.isWaitingForServer = false;
      });
    }
  }



  private showBonusText(text: string, isCorrect: boolean): void {
    // Create or reuse text object
    if (!this.bonusScoreText || !(this.bonusScoreText instanceof Phaser.GameObjects.Text)) {
      // Create text with the appropriate style
      this.bonusScoreText = this.add.text(
        this.cameras.main.width / 2,
        this.cameras.main.height / 2.5,
        text,
        {
          fontFamily: 'Arial',
          fontSize: '32px',
          fontStyle: 'italic',
          color: isCorrect ? '#4FFFAA' : '#FF4F59',
          stroke: '#000000',
          strokeThickness: 3,
          shadow: { offsetX: 1, offsetY: 1, color: '#000000', blur: 2, stroke: true, fill: true }
        }
      ).setOrigin(0.5).setDepth(100).setAlpha(0);

      this.gamePanel.add(this.bonusScoreText);
    } else {
      // Update existing text
      this.bonusScoreText.setText(text);
      this.bonusScoreText.setColor(isCorrect ? '#4FFFAA' : '#FF4F59');
      this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);
    }

    // Animation sequence
    const targetY = this.cameras.main.height / 2.5 - 50; // Move upward

    // Clear any existing tweens
    this.tweens.killTweensOf(this.bonusScoreText);

    // Reset position and alpha
    this.bonusScoreText.setAlpha(0);
    this.bonusScoreText.setPosition(this.cameras.main.width / 2, this.cameras.main.height / 2.5);

    // Create fade in tween
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 1,
      duration: 200,
      ease: 'Linear'
    });

    // Create move up tween
    this.tweens.add({
      targets: this.bonusScoreText,
      y: targetY,
      duration: 700,
      ease: 'Cubic.easeOut'
    });

    // Create fade out tween with delay
    this.tweens.add({
      targets: this.bonusScoreText,
      alpha: 0,
      delay: 600,
      duration: 300,
      ease: 'Linear'
    });
  }

  private endGame(): void {
    if (this.isGameOver) return;

    // Set game over flag
    this.isGameOver = true;
    gameActions.endGame();

    // Play end game sound
    this.sound.play('end');

    // // Transition to end scene after a short delay
    // this.time.delayedCall(500, () => {
    //   // Get current score from gameState store
    //   const currentScore = get(gameState).score;

    //   this.scene.start('GameEndScene', { score: currentScore });
    // });
  }

}
